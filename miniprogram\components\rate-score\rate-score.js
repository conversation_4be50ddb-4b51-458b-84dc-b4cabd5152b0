// components/rate-score/rate-score.js
const api = require('../../utils/api');

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 对比结果缓存ID
    comparisonCacheId: {
      type: String,
      value: ''
    },
    // 当前平均评分
    averageRating: {
      type: Number,
      value: 0
    },
    // 总评分数量
    totalRatings: {
      type: Number,
      value: 0
    },
    // 是否显示评分统计
    showStats: {
      type: Boolean,
      value: true
    },
    // 组件大小 small | medium | large
    size: {
      type: String,
      value: 'medium'
    },
    // 是否禁用评分功能
    disabled: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 用户当前选择的评分
    userRating: 0,
    // 是否正在提交评分
    submitting: false,
    // 用户是否已经评分过
    hasRated: false,
    // 是否显示评分面板
    showRatingPanel: false
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      this.initComponent();
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
    // 移除可能导致循环的observers，直接使用properties中的数据
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 初始化组件
     */
    initComponent() {
      // 检查用户登录状态
      this.checkUserLoginStatus();
    },

    /**
     * 检查用户登录状态
     */
    checkUserLoginStatus() {
      const userInfo = wx.getStorageSync('userInfo');
      if (!userInfo || !userInfo.id) {
        this.setData({
          disabled: true
        });
      }
    },

    /**
     * 显示评分面板
     */
    showRatingPanel() {
      console.log('显示评分面板');
      if (this.properties.disabled || this.data.hasRated) {
        return;
      }

      this.setData({
        showRatingPanel: true
      });
    },

    /**
     * 隐藏评分面板
     */
    hideRatingPanel() {
      console.log('隐藏评分面板');
      this.setData({
        showRatingPanel: false,
        userRating: 0 // 重置用户选择
      });
    },

    /**
     * 评分按钮点击事件
     */
    onStarTap(e) {
      console.log('评分按钮点击事件触发');

      if (this.data.submitting) {
        return;
      }

      const rating = parseInt(e.currentTarget.dataset.rating);
      console.log('用户选择评分:', rating);

      this.setData({
        userRating: rating
      });
    },

    /**
     * 确认评分
     */
    confirmRating() {
      console.log('确认评分:', this.data.userRating);

      if (!this.data.userRating || this.data.submitting) {
        return;
      }

      this.submitRating(this.data.userRating);
    },

    /**
     * 提交评分
     */
    async submitRating(rating) {
      if (!this.properties.comparisonCacheId) {
        wx.showToast({
          title: '缺少对比结果ID',
          icon: 'error'
        });
        return;
      }

      // 检查用户登录状态
      const userInfo = wx.getStorageSync('userInfo');
      if (!userInfo || !userInfo.id) {
        wx.showModal({
          title: '需要登录',
          content: '请先登录后再进行评分',
          confirmText: '去登录',
          success: (res) => {
            if (res.confirm) {
              wx.navigateTo({
                url: '/pages/auth/login/login'
              });
            }
          }
        });
        return;
      }

      this.setData({
        submitting: true
      });

      try {
        const result = await api.user.rateComparison(this.properties.comparisonCacheId, rating);
        
        if (result.success) {
          // 评分成功
          this.setData({
            hasRated: true,
            submitting: false,
            showRatingPanel: false // 隐藏评分面板
          });

          // 更新评分统计
          if (result.data && result.data.updatedRatingStats) {
            const stats = result.data.updatedRatingStats;
            this.setData({
              averageRating: stats.averageRating,
              totalRatings: stats.totalRatings
            });

            // 触发父组件更新事件
            this.triggerEvent('ratingUpdated', {
              averageRating: stats.averageRating,
              totalRatings: stats.totalRatings,
              userRating: rating
            });
          }

          wx.showToast({
            title: '评分成功',
            icon: 'success'
          });

        } else {
          throw new Error(result.message || '评分失败');
        }

      } catch (error) {
        console.error('提交评分失败:', error);
        
        // 重置用户评分
        this.setData({
          userRating: 0,
          submitting: false
        });

        // 处理特定错误
        let errorMessage = '评分失败，请重试';
        if (error.message && error.message.includes('已经评分过了')) {
          errorMessage = '您已经评分过了';
          this.setData({
            hasRated: true
          });
        }

        wx.showToast({
          title: errorMessage,
          icon: 'error'
        });
      }
    },

    /**
     * 获取星星显示状态
     */
    getStarClass(starValue, rating) {
      if (rating >= starValue) {
        return 'star-full';
      } else if (rating >= starValue - 0.5) {
        return 'star-half';
      } else {
        return 'star-empty';
      }
    },

    /**
     * 格式化评分显示
     */
    formatRating(rating) {
      if (!rating || rating === 0) {
        return '0.0';
      }
      return rating.toFixed(1);
    },

    /**
     * 格式化评分数量显示
     */
    formatRatingCount(count) {
      if (!count || count === 0) {
        return '暂无评分';
      }
      return `${count}人评分`;
    }
  }
});
